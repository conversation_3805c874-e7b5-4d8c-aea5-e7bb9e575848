parameters:
  serviceName: ""
  branchName: ""  
  language: ""
  coverageEnabled: false

jobs:
  - job: UnitTest
    displayName: "单元测试"
    pool:
      vmImage: "ubuntu-latest"
    steps:
      # 不需要重复下载代码，使用工作区中已有的代码

      - ${{ if eq(parameters.language, 'python') }}:
          - template: ../steps/unit-test/python-unit-test.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}  
              branchName: ${{ parameters.branchName }}
              coverageEnabled: ${{ parameters.coverageEnabled }}

      - ${{ if eq(parameters.language, 'go') }}:
          - template: ../steps/unit-test/go-unit-test.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}  
              branchName: ${{ parameters.branchName }}
              coverageEnabled: ${{ parameters.coverageEnabled }}

      - ${{ if eq(parameters.language, 'cpp') }}:
          - template: ../steps/unit-test/cpp-unit-test.yml
            parameters:
              serviceName: ${{ parameters.serviceName }}  
              branchName: ${{ parameters.branchName }}
              coverageEnabled: ${{ parameters.coverageEnabled }}
