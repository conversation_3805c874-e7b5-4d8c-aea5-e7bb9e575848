# 示例：如何调用基础模板
# 必须传递 serviceName 和 branchName 参数
# language、sonar、ftp等配置已在base-pipeline中写死

trigger:
  branches:
    include:
      - master
      - Release/*
      - feature/*
variables:
  # 可以通过变量设置服务名称和分支
  serviceName: 'UserService'  # 必须在 service-config.yml 中存在
  branchName: '$(Build.SourceBranch)'  # 使用当前分支
stages:
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: $(serviceName)
      branchName: $(branchName)
