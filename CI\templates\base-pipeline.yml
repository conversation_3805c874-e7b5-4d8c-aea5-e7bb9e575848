parameters:
  serviceName: ""
  branchName: ""
  # 写死的全局配置参数
  ftpServer: "ftp.company.com"
  ftpPort: "21"
  ftpUsername: "deploy_user"
  ftpBasePath: "/uploads"
  sonarUrl: "https://sonar.company.com"
  sonarToken: "squ_1234567890abcdef"
stages:
  - stage: Initialize
    displayName: "初始化配置"
    jobs:
      - job: GetConfig
        pool:
          vmImage: "ubuntu-latest"
        steps:
          - checkout: self
            ref: ${{ parameters.branchName }}  # 下载指定分支代码
          - task: Bash@3
            name: validateParams
            displayName: "验证输入参数"
            inputs:
              targetType: "inline"
              script: |
                # 验证必需参数
                if [ -z "${{ parameters.serviceName }}" ]; then
                  echo "##vso[task.logissue type=error]错误：serviceName参数不能为空"
                  exit 1
                fi
                
                if [ -z "${{ parameters.branchName }}" ]; then
                  echo "##vso[task.logissue type=error]错误：branchName参数不能为空"
                  exit 1
                fi
                
                echo "参数验证通过：serviceName=${{ parameters.serviceName }}, branchName=${{ parameters.branchName }}"
          - task: Bash@3
            name: config
            displayName: "解析服务配置"
            inputs:
              targetType: "inline"
              script: |
                # 读取配置文件（yq工具已在离线环境中预装）
                CONFIG=$(cat service-config.yml)
                SERVICE_CONFIG=$(echo "$CONFIG" | yq eval '.services."${{ parameters.serviceName }}"' -)
                
                # 验证服务配置是否存在
                if [ "$SERVICE_CONFIG" = "null" ]; then
                  echo "##vso[task.logissue type=error]错误：服务 '${{ parameters.serviceName }}' 在配置文件中不存在"
                  exit 1
                fi
                
                # 从服务配置中获取语言信息
                LANGUAGE=$(echo "$SERVICE_CONFIG" | yq eval '.language' -)
                if [ "$LANGUAGE" = "null" ] || [ -z "$LANGUAGE" ]; then
                  echo "##vso[task.logissue type=error]错误：服务 '${{ parameters.serviceName }}' 缺少语言配置"
                  exit 1
                fi

                # 解析各个步骤的 skip 配置
                STATIC_ANALYSIS_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.staticAnalysis.skip // false' -)
                UNIT_TEST_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.unitTest.skip // false' -)
                UNIT_TEST_COVERAGE=$(echo "$SERVICE_CONFIG" | yq eval '.unitTest.coverage // false' -)
                BUILD_SKIP=$(echo "$SERVICE_CONFIG" | yq eval '.build.skip // false' -)

                # 解析架构配置
                ENABLED_ARCHITECTURES=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.enabled == true) | .name' - | tr '\n' ',' | sed 's/,$//')

                # 解析FTP配置
                FTP_ENABLED=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.enabled // false' -)
                

                
                # 解析服务FTP配置
                PATH_TEMPLATE=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.pathTemplate' -)
                FILE_PATTERNS=$(echo "$SERVICE_CONFIG" | yq eval '.deploy.ftp.filePatterns[]' - | tr '\n' ',' | sed 's/,$//')
                
                # 解析启用的架构信息（包含name和artifactName）
                ENABLED_ARCHS_JSON=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.enabled == true) | {"name": .name, "artifactName": .artifactName}' - | yq eval -o=json '.' - | tr '\n' ' ' | sed 's/} {/},{/g')
                ARTIFACT_NAMES=$(echo "$SERVICE_CONFIG" | yq eval '.build.architectures[] | select(.enabled == true) | .artifactName' - | tr '\n' ',' | sed 's/,$//')

                # 设置输出变量
                echo "##vso[task.setvariable variable=language;isOutput=true]$LANGUAGE"
                echo "##vso[task.setvariable variable=staticAnalysis.skip;isOutput=true]$STATIC_ANALYSIS_SKIP"
                echo "##vso[task.setvariable variable=unitTest.skip;isOutput=true]$UNIT_TEST_SKIP"
                echo "##vso[task.setvariable variable=unitTest.coverage;isOutput=true]$UNIT_TEST_COVERAGE"
                echo "##vso[task.setvariable variable=build.skip;isOutput=true]$BUILD_SKIP"
                echo "##vso[task.setvariable variable=build.architectures;isOutput=true]$ENABLED_ARCHITECTURES"
                echo "##vso[task.setvariable variable=deploy.ftp.enabled;isOutput=true]$FTP_ENABLED"
                echo "##vso[task.setvariable variable=ftp.server;isOutput=true]${{ parameters.ftpServer }}"
                echo "##vso[task.setvariable variable=ftp.port;isOutput=true]${{ parameters.ftpPort }}"
                echo "##vso[task.setvariable variable=ftp.username;isOutput=true]${{ parameters.ftpUsername }}"
                echo "##vso[task.setvariable variable=ftp.basePath;isOutput=true]${{ parameters.ftpBasePath }}"
                echo "##vso[task.setvariable variable=ftp.pathTemplate;isOutput=true]$PATH_TEMPLATE"
                echo "##vso[task.setvariable variable=ftp.filePatterns;isOutput=true]$FILE_PATTERNS"
                echo "##vso[task.setvariable variable=ftp.enabledArchs;isOutput=true]$ENABLED_ARCHS_JSON"
                echo "##vso[task.setvariable variable=ftp.artifactNames;isOutput=true]$ARTIFACT_NAMES"
                echo "##vso[task.setvariable variable=sonar.url;isOutput=true]${{ parameters.sonarUrl }}"
                echo "##vso[task.setvariable variable=sonar.token;isOutput=true]${{ parameters.sonarToken }}"

                # 调试输出 - 全局参数
                echo "=== 全局参数 ==="
                echo "Service Name: ${{ parameters.serviceName }}"
                echo "Branch Name: ${{ parameters.branchName }}"
                echo "FTP Server: ${{ parameters.ftpServer }}"
                echo "FTP Port: ${{ parameters.ftpPort }}"
                echo "FTP Username: ${{ parameters.ftpUsername }}"
                echo "FTP Base Path: ${{ parameters.ftpBasePath }}"
                echo "Sonar URL: ${{ parameters.sonarUrl }}"
                echo "Sonar Token: ${{ parameters.sonarToken }}"
                
                # 调试输出 - 服务配置参数
                echo "=== 服务配置参数 ==="
                echo "Language: $LANGUAGE"
                echo "Static Analysis Skip: $STATIC_ANALYSIS_SKIP"
                echo "Unit Test Skip: $UNIT_TEST_SKIP"
                echo "Unit Test Coverage: $UNIT_TEST_COVERAGE"
                echo "Build Skip: $BUILD_SKIP"
                echo "Enabled Architectures: $ENABLED_ARCHITECTURES"
                echo "FTP Upload Enabled: $FTP_ENABLED"
                echo "Path Template: $PATH_TEMPLATE"
                echo "File Patterns: $FILE_PATTERNS"
                echo "Enabled Archs JSON: $ENABLED_ARCHS_JSON"
                echo "Artifact Names: $ARTIFACT_NAMES"

  - stage: StaticAnalysis
    displayName: "静态代码分析 (仅x64架构)"
    dependsOn: Initialize
    condition: and(succeeded(), ne(stageDependencies.Initialize.GetConfig.outputs['config.staticAnalysis.skip'], 'true'), eq(variables['Agent.OSArchitecture'], 'X64'))
    jobs:
      - template: stages/static-analysis.yml
        parameters:
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          language: $[stageDependencies.Initialize.GetConfig.outputs['config.language']]
          sonarUrl: $[stageDependencies.Initialize.GetConfig.outputs['config.sonar.url']]
          sonarToken: $[stageDependencies.Initialize.GetConfig.outputs['config.sonar.token']]

  - stage: UnitTest
    displayName: "单元测试 (仅x64架构)"
    dependsOn: StaticAnalysis
    condition: and(succeeded(), ne(stageDependencies.Initialize.GetConfig.outputs['config.unitTest.skip'], 'true'), eq(variables['Agent.OSArchitecture'], 'X64'))
    jobs:
      - template: stages/unit-test.yml
        parameters:
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          language: $[stageDependencies.Initialize.GetConfig.outputs['config.language']]
          coverageEnabled: $[stageDependencies.Initialize.GetConfig.outputs['config.unitTest.coverage']]

  - stage: Build
    displayName: "多架构构建"
    dependsOn: UnitTest
    condition: and(succeeded(), ne(stageDependencies.Initialize.GetConfig.outputs['config.build.skip'], 'true'))
    jobs:
      - template: stages/multi-arch-build.yml
        parameters:
          serviceName: ${{ parameters.serviceName }}
          branchName: ${{ parameters.branchName }}
          language: $[stageDependencies.Initialize.GetConfig.outputs['config.language']]
          enabledArchitectures: $[stageDependencies.Initialize.GetConfig.outputs['config.build.architectures']]


