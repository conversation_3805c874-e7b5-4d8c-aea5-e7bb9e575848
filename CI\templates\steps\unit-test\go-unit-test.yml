parameters:
  coverageEnabled: false
  serviceName: "" 
  branchName: ""

steps:
  - task: Bash@3
    displayName: "设置覆盖率配置"
    inputs:
      targetType: "inline"
      script: |
        # 使用传入的覆盖率配置
        COVERAGE_ENABLED=${{ parameters.coverageEnabled }}
        
        echo "覆盖率启用状态: $COVERAGE_ENABLED"
        echo "##vso[task.setvariable variable=coverageEnabled]$COVERAGE_ENABLED"

  - task: Bash@3
    displayName: "下载 Go 依赖"
    inputs:
      targetType: "inline"
      script: |
        # 下载依赖（Go已预装在镜像中）
        go mod download
        go mod tidy
        
        echo "Go 依赖下载完成"

  - task: Bash@3
    displayName: "执行 Go 单元测试"
    inputs:
      targetType: "inline"
      script: |
        # 检查是否启用覆盖率
        if [ "$(coverageEnabled)" = "true" ]; then
          echo "执行带覆盖率的 Go 测试..."
          go test -v -coverprofile=coverage.out -covermode=atomic ./... | tee test-results.txt
          
          # 生成覆盖率报告
          go tool cover -html=coverage.out -o coverage.html
          go tool cover -func=coverage.out > coverage-summary.txt
          
          echo "覆盖率报告已生成"
        else
          echo "执行 Go 测试（无覆盖率）..."
          go test -v ./... | tee test-results.txt
        fi
        
        # 检查测试结果
        TEST_EXIT_CODE=${PIPESTATUS[0]}
        if [ $TEST_EXIT_CODE -eq 0 ]; then
          echo "Go 单元测试通过"
        else
          echo "Go 单元测试失败"
          exit $TEST_EXIT_CODE
        fi

  - task: Bash@3
    displayName: "转换测试结果格式"
    inputs:
      targetType: "inline"
      script: |
        # 将Go测试结果转换为JUnit格式（使用go-junit-report工具，已预装）
        if command -v go-junit-report >/dev/null 2>&1; then
          cat test-results.txt | go-junit-report > test-results.xml
          echo "测试结果已转换为JUnit格式"
        else
          echo "go-junit-report 工具未找到，跳过格式转换"
        fi
    condition: succeededOrFailed()

  - task: PublishTestResults@2
    displayName: "发布测试结果"
    inputs:
      testResultsFormat: "JUnit"
      testResultsFiles: "**/test-results.xml"
      failTaskOnFailedTests: true
    condition: succeededOrFailed()

  - task: PublishCodeCoverageResults@1
    displayName: "发布覆盖率报告"
    inputs:
      codeCoverageToolType: "Cobertura"
      summaryFileLocation: "**/coverage.out"
      reportDirectory: "**/coverage.html"
    condition: and(succeededOrFailed(), eq(variables['coverageEnabled'], 'true'))
