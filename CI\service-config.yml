# 服务配置 - 简化版本，只保留核心业务配置
services:
  # Python服务示例
  UserService:
    language: python
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "UserService-python-x86"
        - name: "arm64"
          enabled: true
          artifactName: "UserService-python-arm64"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/UserService/{architecture}"
        filePatterns: ["*.whl", "*.tar.gz"]

  # Go服务示例
  OrderService:
    language: go
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "OrderService-go-x86"
        - name: "arm64"
          enabled: true
          artifactName: "OrderService-go-arm64"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/OrderService/{architecture}"
        filePatterns: ["app", "*.exe"]

  # C++服务示例
  PaymentService:
    language: cpp
    staticAnalysis:
      skip: false
    unitTest:
      skip: false
      coverage: true
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "PaymentService-cpp-x86"
    deploy:
      ftp:
        enabled: false
        pathTemplate: "/services/PaymentService/{architecture}"
        filePatterns: ["*.so", "*.a", "PaymentService"]

  # 更多服务可以用更简洁的配置
  NotificationService:
    language: python
    staticAnalysis: { skip: false }
    unitTest: { skip: false, coverage: true }
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "NotificationService-python-x86"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/NotificationService/{architecture}"
        filePatterns: ["*.whl", "*.tar.gz"]

  LogService:
    language: go
    staticAnalysis: { skip: true }
    unitTest: { skip: false, coverage: false }
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "LogService-go-x86"
        - name: "arm64"
          enabled: true
          artifactName: "LogService-go-arm64"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/LogService/{architecture}"
        filePatterns: ["app", "*.exe"]

  # 批量配置示例 - 对于相似的服务可以使用更简洁的语法
  AuthService:
    language: python
    staticAnalysis: {skip: false}
    unitTest: {skip: false, coverage: true}
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "AuthService-python-x86"
        - name: "arm64"
          enabled: true
          artifactName: "AuthService-python-arm64"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/AuthService/{architecture}"
        filePatterns: ["*.whl", "*.tar.gz"]
        
  ConfigService:
    language: go
    staticAnalysis: {skip: false}
    unitTest: {skip: false, coverage: true}
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "ConfigService-go-x86"
    deploy:
      ftp:
        enabled: false
        pathTemplate: "/services/ConfigService/{architecture}"
        filePatterns: ["app", "*.exe"]
        
  MonitorService:
    language: cpp
    staticAnalysis: {skip: true}
    unitTest: {skip: false, coverage: false}
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "MonitorService-cpp-x86"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/MonitorService/{architecture}"
        filePatterns: ["*.so", "*.a", "MonitorService"]
