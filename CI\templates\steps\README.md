# Steps 目录结构说明

本目录按照 Azure DevOps Pipeline 的 stages 进行组织，每个子目录对应一个主要的构建阶段。

## 目录结构

```
templates/steps/
├── static-analysis/     # 静态代码分析相关步骤
├── unit-test/          # 单元测试相关步骤
├── build/              # 构建相关步骤
├── deploy/             # 部署相关步骤
└── README.md           # 本说明文件
```

## 各目录说明

### static-analysis/ - 静态代码分析
包含各种编程语言的静态代码分析步骤模板：

- `python-static-analysis.yml` - Python静态分析（flake8, pylint）
- `go-static-analysis.yml` - Go静态分析（golint, staticcheck, gosec）
- `cpp-static-analysis.yml` - C++静态分析（cppcheck, clang-tidy）
- `python-analysis.yml` - Python分析（旧版本，待整合）

### unit-test/ - 单元测试
包含各种编程语言的单元测试步骤模板：

- `python-unit-test.yml` - Python单元测试（pytest, unittest）
- `go-unit-test.yml` - Go单元测试（go test）
- `cpp-unit-test.yml` - C++单元测试（Google Test）

### build/ - 构建
包含各种编程语言的构建步骤模板：

#### 单架构构建
- `python-build.yml` - Python单架构构建
- `go-build.yml` - Go单架构构建
- `cpp-build.yml` - C++单架构构建

#### 多架构构建
- `python-build-arch.yml` - Python多架构构建（x86, arm64）
- `go-build-arch.yml` - Go多架构构建（x86, arm64）
- `cpp-build-arch.yml` - C++多架构构建（x86, arm64）

### deploy/ - 部署
包含部署相关的步骤模板：

- 目前为空，预留给FTP上传等部署步骤

## 使用方式

在 stages 模板中引用这些步骤模板时，使用相对路径：

```yaml
# 在 templates/stages/static-analysis.yml 中
- template: ../steps/static-analysis/python-static-analysis.yml
  parameters:
    serviceName: ${{ parameters.serviceName }}
    sonarUrl: ${{ parameters.sonarUrl }}
    sonarToken: ${{ parameters.sonarToken }}

# 在 templates/stages/unit-test.yml 中  
- template: ../steps/unit-test/go-unit-test.yml
  parameters:
    serviceName: ${{ parameters.serviceName }}
    coverageEnabled: ${{ parameters.coverageEnabled }}

# 在 templates/stages/multi-arch-build.yml 中
- template: ../steps/build/cpp-build-arch.yml
  parameters:
    buildImage: ${{ parameters.buildImage }}
    artifactName: ${{ parameters.artifactName }}
    architecture: 'x86'
```

## 支持的编程语言

- **Python** - 完整支持静态分析、单元测试、单/多架构构建
- **Go** - 完整支持静态分析、单元测试、单/多架构构建  
- **C++** - 完整支持静态分析、单元测试、单/多架构构建

## 架构支持

- **x86 (amd64)** - 所有语言都支持
- **arm64** - 所有语言都支持多架构构建
- **静态分析和单元测试** - 固定在x86架构运行以提高效率

## 扩展说明

如需添加新的编程语言或架构支持：

1. 在对应的目录下创建新的步骤模板文件
2. 在相应的 stages 模板中添加条件引用
3. 在 service-config.yml 中添加对应的配置示例
4. 更新本README文件的说明
