# 简化的服务配置文件 - 适用于大量服务管理
# 工具和脚本配置已移至 templates/config/{language}-config.yml
# 服务配置 - 极简版本，只保留必要的业务配置
services:
  # Python服务 (微服务架构)
  UserService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [{name: "x86", enabled: true, artifactName: "UserService-python-x86"}, {name: "arm64", enabled: true, artifactName: "UserService-python-arm64"}]}, deploy: {ftp: {enabled: true, pathTemplate: "/services/UserService/{architecture}", filePatterns: ["*.whl", "*.tar.gz"]}} }
  OrderService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [{name: "x86", enabled: true, artifactName: "OrderService-python-x86"}, {name: "arm64", enabled: true, artifactName: "OrderService-python-arm64"}]}, deploy: {ftp: {enabled: true, pathTemplate: "/services/OrderService/{architecture}", filePatterns: ["*.whl", "*.tar.gz"]}} }
  PaymentService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [{name: "x86", enabled: true, artifactName: "PaymentService-python-x86"}, {name: "arm64", enabled: true, artifactName: "PaymentService-python-arm64"}]}, deploy: {ftp: {enabled: true, pathTemplate: "/services/PaymentService/{architecture}", filePatterns: ["*.whl", "*.tar.gz"]}} }
  NotificationService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [{name: "x86", enabled: true, artifactName: "NotificationService-python-x86"}]}, deploy: {ftp: {enabled: true, pathTemplate: "/services/NotificationService/{architecture}", filePatterns: ["*.whl", "*.tar.gz"]}} }
  AuthService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [{name: "x86", enabled: true, artifactName: "AuthService-python-x86"}, {name: "arm64", enabled: true, artifactName: "AuthService-python-arm64"}]}, deploy: {ftp: {enabled: true, pathTemplate: "/services/AuthService/{architecture}", filePatterns: ["*.whl", "*.tar.gz"]}} }
  ConfigService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86]}, deploy: {ftp: {enabled: false}} }
  ReportService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  AnalyticsService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  FileService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  SearchService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }

  # Go服务 (API网关和高性能服务)
  APIGateway: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  LoadBalancer: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  MessageQueue: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  CacheService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  LogService: { language: go, staticAnalysis: {skip: true}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  MonitorService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  ProxyService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  DiscoveryService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  HealthCheckService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86]}, deploy: {ftp: {enabled: false}} }
  MetricsService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }

  # C++服务 (高性能计算和系统服务)
  ComputeEngine: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  DatabaseEngine: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  NetworkEngine: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  CryptoService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  CompressionService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86]}, deploy: {ftp: {enabled: false}} }

  # 批量配置示例 - 更多服务
  BackupService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  SyncService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  ValidationService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: false}} }
  TransformService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: false}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  SchedulerService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  WorkflowService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  EventService: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  AuditService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  SecurityService: { language: cpp, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: true}} }
  ComplianceService: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86]}, deploy: {ftp: {enabled: false}} }

# 注释说明:
# 1. 所有工具配置(flake8, pylint, golint等)都移到了 templates/config/{language}-config.yml
# 2. 所有构建脚本都移到了语言配置文件中
# 3. 服务配置只保留核心业务决策: 是否跳过、启用覆盖率、支持架构、是否部署
# 4. 使用紧凑的YAML语法减少配置文件大小
# 5. 可以通过 scripts/config-manager.py 工具批量生成和管理配置
