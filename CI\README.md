# Azure DevOps 企业级 CI/CD 模板生成器

这是一个面向企业级应用的 Azure DevOps 多语言、多架构 CI/CD 模板生成器，专为大规模微服务架构设计，支持 Python、Go 和 C++ 项目的自动化构建、测试和部署。

## 🏗️ 架构设计

### 整体架构

```
┌─────────────────────────────────────────────────────────────────┐
│                    Azure DevOps Pipeline                        │
├─────────────────────────────────────────────────────────────────┤
│  📋 Initialize Stage                                           │
│  ├── 参数验证 (serviceName, branchName)                        │
│  ├── 服务配置解析 (service-config.yml)                         │
│  └── 全局变量设置 (language, architectures, deploy settings)    │
├─────────────────────────────────────────────────────────────────┤
│  🔍 Static Analysis Stage (仅 x64 架构)                        │
│  ├── Python: flake8, pylint, bandit + SonarQube              │
│  ├── Go: golint, go vet, gosec + SonarQube                    │
│  └── C++: cppcheck, clang-tidy + SonarQube                    │
├─────────────────────────────────────────────────────────────────┤
│  🧪 Unit Test Stage (仅 x64 架构)                              │
│  ├── Python: pytest + coverage                                │
│  ├── Go: go test + coverage                                    │
│  └── C++: gtest + lcov                                         │
├─────────────────────────────────────────────────────────────────┤
│  🔨 Multi-Architecture Build Stage                             │
│  ├── x86 Build Job                                             │
│  │   ├── Python: wheel + tar.gz                               │
│  │   ├── Go: 交叉编译二进制                                     │
│  │   └── C++: CMake + Make                                     │
│  └── ARM64 Build Job                                           │
│      ├── Python: wheel + tar.gz                               │
│      ├── Go: 交叉编译二进制                                     │
│      └── C++: 交叉编译 + CMake                                 │
├─────────────────────────────────────────────────────────────────┤
│  🚀 Deploy Stage                                               │
│  └── FTP Upload (所有架构产物)                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 核心设计原则

1. **配置驱动**: 所有服务配置通过 `service-config.yml` 统一管理
2. **模板复用**: 采用分层模板设计，最大化代码复用
3. **架构隔离**: 静态分析和测试仅在 x64 执行，构建支持多架构
4. **离线优先**: 所有工具预装在构建镜像中，适配企业内网环境
5. **标准化**: 统一的代码质量标准和测试框架

## 🚀 核心特性

### 多语言支持
- **Python**: 支持 setuptools、wheel 打包，pytest 测试框架
- **Go**: 支持模块化构建，内置测试和基准测试
- **C++**: 支持 CMake 构建系统，GTest 测试框架

### 多架构构建
- **x86**: 标准 x86_64 架构支持
- **ARM64**: 完整的 ARM64 交叉编译支持
- **动态配置**: 每个服务可独立配置支持的架构

### 企业级特性
- **离线环境**: 完全适配无外网连接的企业内网环境
- **统一质量门禁**: SonarQube 集成的代码质量分析
- **标准化流程**: 统一的构建、测试、部署流程
- **配置管理**: 集中式服务配置管理
- **产物管理**: 自动化的构建产物上传和版本管理

## 📋 快速开始

### 1. 基本使用

在您的项目根目录创建 `azure-pipelines.yml` 文件：

```yaml
# azure-pipelines.yml
trigger:
  branches:
    include:
      - main
      - develop
      - feature/*
      - release/*

variables:
  serviceName: 'UserService'  # 必须在 service-config.yml 中存在
  branchName: '$(Build.SourceBranch)'  # 使用当前分支

stages:
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: $(serviceName)
      branchName: $(branchName)
```

### 2. 高级配置

```yaml
# 多服务流水线示例
stages:
  # 用户服务
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
  
  # 订单服务
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'OrderService'
      branchName: 'refs/heads/main'  # 指定特定分支
```

### 3. 条件执行

```yaml
# 基于分支条件执行
stages:
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
    condition: or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'))
```

## ⚙️ 配置管理

### 必需参数

调用基础模板时，**必须**提供以下参数：

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| `serviceName` | string | ✅ | 服务名称，必须在 `service-config.yml` 中存在 |
| `branchName` | string | ✅ | 分支名称，用于下载指定分支的代码 |

### 服务配置文件 (service-config.yml)

这是整个系统的核心配置文件，定义了所有服务的构建、测试和部署配置：

```yaml
services:
  UserService:
    language: python              # 必需：语言类型 (python/go/cpp)
    staticAnalysis:
      skip: false                 # 是否跳过静态分析
    unitTest:
      skip: false                 # 是否跳过单元测试
      coverage: true              # 是否启用覆盖率
    build:
      architectures:              # 支持的架构列表
        - name: "x86"
          enabled: true
          artifactName: "UserService-python-x86"
        - name: "arm64"
          enabled: true
          artifactName: "UserService-python-arm64"
    deploy:
      ftp:
        enabled: true             # 是否启用FTP部署
        pathTemplate: "/services/UserService/{architecture}"
        filePatterns: ["*.whl", "*.tar.gz"]
```

### 配置字段说明

#### 语言配置 (language)
- `python`: Python 项目，使用 setuptools/wheel 打包
- `go`: Go 项目，使用 go build 编译
- `cpp`: C++ 项目，使用 CMake 构建

#### 静态分析配置 (staticAnalysis)
- `skip`: 是否跳过静态分析阶段
- 工具链：Python(flake8+pylint), Go(golint+go vet), C++(cppcheck+clang-tidy)

#### 单元测试配置 (unitTest)
- `skip`: 是否跳过单元测试阶段
- `coverage`: 是否启用代码覆盖率收集
- 框架：Python(pytest), Go(go test), C++(gtest)

#### 构建配置 (build.architectures)
- `name`: 架构名称 ("x86", "arm64")
- `enabled`: 是否启用该架构的构建
- `artifactName`: 构建产物的名称

#### 部署配置 (deploy.ftp)
- `enabled`: 是否启用FTP部署
- `pathTemplate`: FTP路径模板，支持变量替换
- `filePatterns`: 要上传的文件模式列表

### 失败场景

以下情况将导致流水线失败：

1. ❌ 缺少 `serviceName` 参数
2. ❌ 缺少 `branchName` 参数
3. ❌ 指定的服务在 `service-config.yml` 中不存在
4. ❌ 服务配置中缺少 `language` 字段
5. ❌ 架构配置格式不正确
6. ❌ YAML 语法错误

## 🏗️ 技术架构

### 执行架构策略

| 阶段 | 执行架构 | 原因 |
|------|----------|------|
| 静态分析 | 仅 x64 | 分析工具兼容性，避免重复执行 |
| 单元测试 | 仅 x64 | 测试逻辑与架构无关，提高效率 |
| 构建 | 多架构 | 生成不同架构的可执行文件 |
| 部署 | 单次 | 统一收集所有架构产物并上传 |

### 工具链矩阵

| 语言 | 静态分析工具 | 测试框架 | 构建工具 | 打包格式 |
|------|-------------|----------|----------|----------|
| Python | flake8, pylint, bandit | pytest | setuptools | wheel, tar.gz |
| Go | golint, go vet, gosec | go test | go build | binary |
| C++ | cppcheck, clang-tidy | gtest | cmake, make | binary, .so |

### 镜像配置

```yaml
# 预配置的构建镜像
Python:
  x86: "python:3.9-slim"     # 包含 pytest, flake8, pylint
  arm64: "python:3.9-slim"   # 支持交叉编译

Go:
  x86: "golang:1.19"         # 包含 golint, go vet
  arm64: "golang:1.19"       # 内置交叉编译支持

C++:
  x86: "ubuntu:20.04"        # 包含 gcc, cmake, gtest
  arm64: "ubuntu:20.04"      # 包含 gcc-aarch64-linux-gnu
```

## 📁 项目结构

```
azure-devops-template-generator/
├── 📋 配置文件
│   ├── service-config.yml         # 主服务配置文件
│   ├── service-config-simple.yml  # 简化配置示例
│   └── example-azure-pipelines.yml # 使用示例
├── 🎯 核心模板
│   └── templates/
│       ├── base-pipeline.yml      # 主入口模板
│       ├── stages/                 # 阶段级模板
│       │   ├── static-analysis.yml # 静态分析阶段
│       │   ├── unit-test.yml      # 单元测试阶段
│       │   ├── multi-arch-build.yml # 多架构构建阶段
│       │   └── ftp-upload.yml     # FTP部署阶段
│       └── steps/                  # 步骤级模板
│           ├── static-analysis/    # 各语言静态分析步骤
│           │   ├── python-static-analysis.yml
│           │   ├── go-static-analysis.yml
│           │   └── cpp-static-analysis.yml
│           ├── unit-test/         # 各语言单元测试步骤
│           │   ├── python-unit-test.yml
│           │   ├── go-unit-test.yml
│           │   └── cpp-unit-test.yml
│           └── build/             # 各语言构建步骤
│               ├── python-build-arch.yml
│               ├── go-build-arch.yml
│               └── cpp-build-arch.yml
├── 🛠️ 工具脚本
│   └── scripts/
│       └── config-manager.py      # 配置管理工具
├── 📚 文档
│   └── docs/
│       └── CONFIG_MANAGEMENT.md   # 配置管理文档
└── 📖 说明文档
    └── README.md                   # 本文档
```

## 📚 详细使用手册

### 环境准备

1. **Azure DevOps 环境**
   ```bash
   # 确保您的 Azure DevOps 项目已启用以下功能：
   - Azure Pipelines
   - Azure Artifacts (可选，用于存储构建产物)
   - SonarQube 集成 (需要配置 SonarQube 服务连接)
   ```

2. **变量组配置**
   ```yaml
   # 在 Azure DevOps 中创建变量组 "Global-Config"
   变量名称              | 值                    | 说明
   FTP_PASSWORD         | your-ftp-password    | FTP 服务器密码
   SONAR_TOKEN         | your-sonar-token     | SonarQube 访问令牌
   ```

3. **服务连接配置**
   - SonarQube 服务连接：用于代码质量分析
   - FTP 服务连接：用于构建产物部署

### 项目集成步骤

#### 步骤 1: 复制模板文件
```bash
# 将整个 templates 目录复制到您的项目根目录
cp -r azure-devops-template-generator/templates ./
cp azure-devops-template-generator/service-config.yml ./
```

#### 步骤 2: 配置服务信息
编辑 `service-config.yml` 文件，添加您的服务配置：

```yaml
services:
  YourService:
    language: python              # 选择语言: python/go/cpp
    staticAnalysis:
      skip: false                 # 是否跳过静态分析
    unitTest:
      skip: false                 # 是否跳过单元测试
      coverage: true              # 是否启用覆盖率
    build:
      architectures:
        - name: "x86"
          enabled: true
          artifactName: "YourService-python-x86"
        - name: "arm64"
          enabled: true
          artifactName: "YourService-python-arm64"
    deploy:
      ftp:
        enabled: true
        pathTemplate: "/services/YourService/{architecture}"
        filePatterns: ["*.whl", "*.tar.gz"]
```

#### 步骤 3: 创建流水线文件
在项目根目录创建 `azure-pipelines.yml`：

```yaml
trigger:
  branches:
    include:
      - main
      - develop
      - feature/*
      - release/*

variables:
  serviceName: 'YourService'  # 与 service-config.yml 中的服务名一致
  branchName: '$(Build.SourceBranch)'

stages:
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: $(serviceName)
      branchName: $(branchName)
```

#### 步骤 4: 项目代码结构
确保您的项目符合以下结构要求：

**Python 项目:**
```
your-python-service/
├── setup.py                 # 必需：用于构建
├── requirements.txt         # 必需：依赖列表
├── requirements-test.txt    # 可选：测试依赖
├── src/                     # 源代码目录
└── tests/                   # 测试代码目录
```

**Go 项目:**
```
your-go-service/
├── go.mod                   # 必需：Go 模块文件
├── go.sum                   # 必需：依赖校验
├── main.go                  # 主程序入口
├── pkg/                     # 包目录
└── *_test.go               # 测试文件
```

**C++ 项目:**
```
your-cpp-service/
├── CMakeLists.txt          # 必需：CMake 构建文件
├── src/                    # 源代码目录
├── include/                # 头文件目录
└── tests/                  # 测试代码目录
```

### 高级配置

#### 多服务项目
对于包含多个服务的单体仓库：

```yaml
# azure-pipelines.yml
stages:
  # 并行构建多个服务
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
  
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'OrderService'
      branchName: '$(Build.SourceBranch)'
  
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'PaymentService'
      branchName: '$(Build.SourceBranch)'
```

#### 条件构建
基于分支或路径变更的条件构建：

```yaml
# 仅在特定路径变更时构建
stages:
  - template: templates/base-pipeline.yml
    parameters:
      serviceName: 'UserService'
      branchName: '$(Build.SourceBranch)'
    condition: |
      or(
        eq(variables['Build.SourceBranch'], 'refs/heads/main'),
        startsWith(variables['Build.SourceBranch'], 'refs/heads/release/'),
        contains(variables['Build.SourcesDirectory'], 'user-service/')
      )
```

## 🎯 最佳实践

### 代码组织

1. **目录结构标准化**
   ```
   # 推荐的项目结构
   your-service/
   ├── azure-pipelines.yml      # 流水线定义
   ├── service-config.yml       # 服务配置
   ├── templates/               # CI/CD 模板
   ├── src/                     # 源代码
   ├── tests/                   # 测试代码
   ├── docs/                    # 文档
   └── scripts/                 # 构建脚本
   ```

2. **分支策略**
   ```yaml
   # 推荐的分支触发配置
   trigger:
     branches:
       include:
         - main                   # 主分支
         - develop               # 开发分支
         - release/*             # 发布分支
         - hotfix/*              # 热修复分支
       exclude:
         - feature/experimental  # 排除实验性分支
   ```

3. **服务命名规范**
   ```yaml
   # 服务名称应遵循以下规范：
   - 使用 PascalCase: UserService, OrderService
   - 避免特殊字符和空格
   - 保持简洁且具有描述性
   - 与实际服务名称保持一致
   ```

### 性能优化

1. **并行执行**
   ```yaml
   # 利用 Azure DevOps 的并行能力
   stages:
     - stage: ParallelBuild
       jobs:
         - template: templates/base-pipeline.yml
           parameters:
             serviceName: 'Service1'
         - template: templates/base-pipeline.yml
           parameters:
             serviceName: 'Service2'
   ```

2. **缓存策略**
   ```yaml
   # 在模板中已内置依赖缓存
   # Python: pip cache
   # Go: module cache
   # C++: build cache
   ```

3. **架构选择**
   ```yaml
   # 根据需求选择架构
   build:
     architectures:
       - name: "x86"           # 开发和测试环境
         enabled: true
       - name: "arm64"         # 生产环境（如果需要）
         enabled: false        # 可以暂时禁用以加快构建
   ```

### 安全最佳实践

1. **敏感信息管理**
   ```yaml
   # 使用 Azure DevOps 变量组存储敏感信息
   variables:
   - group: 'Security-Secrets'    # 包含 FTP_PASSWORD, SONAR_TOKEN
   - group: 'Environment-Config'  # 包含环境特定配置
   ```

2. **权限控制**
   ```
   - 限制对生产环境的部署权限
   - 使用服务连接而非硬编码凭据
   - 定期轮换访问令牌
   ```

## 🔧 故障排除

### 常见问题

#### 1. 服务配置未找到
```
错误: 服务 'XXXService' 在 service-config.yml 中不存在
解决: 检查服务名称拼写，确保在 service-config.yml 中存在对应配置
```

#### 2. 架构配置错误
```
错误: 架构配置格式不正确
解决: 确保架构配置包含 name, enabled, artifactName 字段
正确格式:
architectures:
  - name: "x86"
    enabled: true
    artifactName: "Service-lang-x86"
```

#### 3. 构建工具未找到
```
错误: command not found: go/python/cmake
解决: 检查构建镜像配置，确保使用了正确的预配置镜像
```

#### 4. SonarQube 连接失败
```
错误: SonarQube 服务器连接超时
解决: 
1. 检查 SonarQube 服务连接配置
2. 验证 SONAR_TOKEN 是否有效
3. 确认网络连接正常
```

#### 5. FTP 上传失败
```
错误: FTP 连接被拒绝
解决:
1. 检查 FTP 服务器地址和端口
2. 验证 FTP_PASSWORD 变量
3. 确认 FTP 服务器允许来自 Azure DevOps 的连接
```

### 调试技巧

1. **启用详细日志**
   ```yaml
   # 在模板中已内置详细日志输出
   # 查看构建日志中的详细信息
   ```

2. **本地测试**
   ```bash
   # 在本地环境测试构建脚本
   # Python
   python setup.py build
   pytest tests/
   
   # Go
   go mod download
   go test ./...
   go build
   
   # C++
   mkdir build && cd build
   cmake ..
   make
   ```

3. **分阶段调试**
   ```yaml
   # 临时禁用某些阶段进行调试
   staticAnalysis:
     skip: true  # 跳过静态分析
   unitTest:
     skip: true  # 跳过单元测试
   ```

## 📝 版本历史

### v4.0.0 (当前版本)

- 🎯 **架构重构**: 完整的企业级架构设计文档
- 📚 **文档完善**: 详细的使用手册和最佳实践指南
- 🔧 **配置优化**: 统一的配置管理和验证机制
- 🚀 **性能提升**: 优化的并行构建和缓存策略
- 🛡️ **安全增强**: 完善的安全最佳实践和权限控制
- 🔍 **故障排除**: 详细的问题诊断和解决方案

### v3.0.0

- ✅ **配置优化**: SonarQube、FTP、全局配置已内置在base-pipeline中
- ✅ **架构判断修正**: 修正x64架构判断条件（Agent.OSArchitecture = 'X64'）
- ✅ **镜像配置**: 各语言和架构的构建镜像已预配置在模板中
- ✅ **部署优化**: 部署上传改为在各架构构建完成后执行
- ✅ **SonarQube优化**: sonar.projectKey统一使用服务名
- ✅ **配置解析优化**: 所有配置参数在base-pipeline中统一解析和传递
- ✅ **工具优化**: 移除所有工具下载，依赖镜像预装工具
- ✅ **文件清理**: 删除无用的配置文件和模板文件

### v2.0.0

- ✅ 添加分支支持，支持指定分支构建
- ✅ 移除 `language` 参数，从服务配置自动获取
- ✅ 添加参数验证，确保必需参数存在
- ✅ 添加架构限制条件，静态分析和单元测试仅在 x64 执行
- ✅ 适配离线环境，移除所有工具安装步骤
- ✅ 统一静态分析工具为 SonarQube
- ✅ 标准化单元测试框架

### v1.0.0

- ✅ 初始版本发布
- ✅ 支持 Python、Go、C++ 多语言构建
- ✅ 支持 x86、x64、arm64 多架构构建
- ✅ 集成 SonarQube 静态代码分析
- ✅ 集成单元测试和代码覆盖率
- ✅ 支持 FTP 自动部署

## 🤝 贡献指南

我们欢迎社区贡献！请遵循以下指南：

### 贡献流程

1. **Fork 项目**
   ```bash
   # 克隆你的 fork
   git clone https://github.com/your-username/azure-devops-template-generator.git
   cd azure-devops-template-generator
   ```

2. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **提交更改**
   ```bash
   git add .
   git commit -m "feat: 添加新功能描述"
   ```

4. **推送并创建 Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

### 代码规范

1. **YAML 格式**
   - 使用 2 个空格缩进
   - 保持一致的命名约定
   - 添加必要的注释

2. **提交信息格式**
   ```
   type(scope): description
   
   [optional body]
   
   [optional footer]
   ```
   
   类型包括：
   - `feat`: 新功能
   - `fix`: 修复 bug
   - `docs`: 文档更新
   - `style`: 代码格式调整
   - `refactor`: 代码重构
   - `test`: 测试相关
   - `chore`: 构建过程或辅助工具的变动

3. **测试要求**
   - 新功能必须包含相应的测试
   - 确保所有现有测试通过
   - 更新相关文档

### 问题报告

如果发现 bug 或有功能建议，请：

1. 检查是否已有相关 issue
2. 使用提供的 issue 模板
3. 提供详细的复现步骤
4. 包含相关的日志和配置信息

## 📞 支持与联系

- **文档**: 查看本 README 和项目 Wiki
- **问题反馈**: 通过 GitHub Issues 提交
- **功能请求**: 通过 GitHub Issues 提交功能建议
- **讨论**: 参与 GitHub Discussions

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

```
MIT License

Copyright (c) 2024 Azure DevOps Template Generator

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

**🎉 感谢使用 Azure DevOps Template Generator！**

如果这个项目对你有帮助，请考虑给我们一个 ⭐ Star！