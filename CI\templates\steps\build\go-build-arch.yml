parameters:
  buildImage: ""
  artifactName: ""
  architecture: ""
  serviceName: ""
  branchName: ""
steps:
  - task: Bash@3
    displayName: "设置Go构建配置"
    name: parseArchConfig
    inputs:
      targetType: "inline"
      script: |
        ARCH='${{ parameters.architecture }}'
        BUILD_IMAGE='${{ parameters.buildImage }}'
        ARTIFACT_NAME='${{ parameters.artifactName }}'
        
        echo "##vso[task.setvariable variable=artifactName]$ARTIFACT_NAME"
        echo "##vso[task.setvariable variable=buildImage]$BUILD_IMAGE"
        
        echo "Architecture: $ARCH"
        echo "Build Image: $BUILD_IMAGE"
        echo "Artifact Name: $ARTIFACT_NAME"

  - task: GoTool@0
    inputs:
      version: "1.18"
    displayName: "安装Go (${{ parameters.architecture }})"

  - task: Bash@3
    displayName: "设置Go多架构构建环境"
    inputs:
      targetType: "inline"
      script: |
        # 根据架构设置Go环境变量
        case "${{ parameters.architecture }}" in
          "arm64")
            echo "##vso[task.setvariable variable=GOOS]linux"
            echo "##vso[task.setvariable variable=GOARCH]arm64"
            echo "##vso[task.setvariable variable=CGO_ENABLED]0"
            ;;
          "x86")
            echo "##vso[task.setvariable variable=GOOS]linux"
            echo "##vso[task.setvariable variable=GOARCH]amd64"
            echo "##vso[task.setvariable variable=CGO_ENABLED]0"
            ;;
        esac
        
        echo "Go环境设置完成:"
        echo "GOOS: $GOOS"
        echo "GOARCH: $GOARCH"
        echo "CGO_ENABLED: $CGO_ENABLED"

  - task: Bash@3
    displayName: "执行Go构建 (${{ parameters.architecture }})"
    container: ${{ parameters.buildImage }}
    inputs:
      targetType: "inline"
      script: |
        # 设置Go环境变量
        export GOOS=$GOOS
        export GOARCH=$GOARCH
        export CGO_ENABLED=$CGO_ENABLED
        
        # 创建架构特定的目录
        mkdir -p "bin/${{ parameters.architecture }}"
        
        # 执行构建命令
        echo "下载Go模块依赖..."
        go mod download
        
        echo "整理Go模块..."
        go mod tidy
        
        echo "构建Go应用..."
        go build -o "bin/${{ parameters.architecture }}/app" .
        
        # 确保二进制文件在正确的目录中
        if [ -d "bin" ]; then
          # 移动构建产物到架构特定目录
          find bin -name "*" -type f | while read file; do
            if [[ "$file" != *"/${{ parameters.architecture }}/"* ]]; then
              filename=$(basename "$file")
              mv "$file" "bin/${{ parameters.architecture }}/${filename}-${{ parameters.architecture }}"
            fi
          done
        fi
        
        echo "构建完成，产物位于: bin/${{ parameters.architecture }}/"
        ls -la "bin/${{ parameters.architecture }}/"

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: "bin/${{ parameters.architecture }}"
      artifactName: "$(artifactName)"
    displayName: "发布构建产物 (${{ parameters.architecture }})"
