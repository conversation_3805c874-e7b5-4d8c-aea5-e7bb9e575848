parameters:
  serviceName: ""
  branchName: ""
  buildImage: ""
  artifactName: ""
  architecture: ""

steps:
  - task: Bash@3
    displayName: "设置C++构建配置"
    name: parseArchConfig
    inputs:
      targetType: "inline"
      script: |
        ARCH='${{ parameters.architecture }}'
        BUILD_IMAGE='${{ parameters.buildImage }}'
        ARTIFACT_NAME='${{ parameters.artifactName }}'
        
        echo "##vso[task.setvariable variable=artifactName]$ARTIFACT_NAME"
        echo "##vso[task.setvariable variable=buildImage]$BUILD_IMAGE"
        
        echo "Architecture: $ARCH"
        echo "Build Image: $BUILD_IMAGE"
        echo "Artifact Name: $ARTIFACT_NAME"

  - task: Bash@3
    displayName: "安装C++构建工具"
    container: ${{ parameters.buildImage }}
    inputs:
      targetType: "inline"
      script: |
        # 更新包管理器
        sudo apt-get update
        
        # 安装基础构建工具
        sudo apt-get install -y build-essential cmake ninja-build
        
        # 根据架构安装交叉编译工具
        case "${{ parameters.architecture }}" in
          "arm64")
            sudo apt-get install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu
            echo "##vso[task.setvariable variable=CMAKE_TOOLCHAIN_FILE]cmake/arm64-toolchain.cmake"
            echo "##vso[task.setvariable variable=CC]aarch64-linux-gnu-gcc"
            echo "##vso[task.setvariable variable=CXX]aarch64-linux-gnu-g++"
            ;;
          "x86")
            echo "##vso[task.setvariable variable=CMAKE_TOOLCHAIN_FILE]"
            echo "##vso[task.setvariable variable=CC]gcc"
            echo "##vso[task.setvariable variable=CXX]g++"
            ;;
        esac

  - task: Bash@3
    displayName: "创建ARM64工具链文件"
    condition: eq('${{ parameters.architecture }}', 'arm64')
    inputs:
      targetType: "inline"
      script: |
        mkdir -p cmake
        cat > cmake/arm64-toolchain.cmake << 'EOF'
        set(CMAKE_SYSTEM_NAME Linux)
        set(CMAKE_SYSTEM_PROCESSOR aarch64)
        
        set(CMAKE_C_COMPILER aarch64-linux-gnu-gcc)
        set(CMAKE_CXX_COMPILER aarch64-linux-gnu-g++)
        
        set(CMAKE_FIND_ROOT_PATH /usr/aarch64-linux-gnu)
        set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
        set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
        set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
        set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)
        EOF
        
        echo "ARM64工具链文件已创建"
        cat cmake/arm64-toolchain.cmake

  - task: Bash@3
    displayName: "执行C++构建 (${{ parameters.architecture }})"
    container: ${{ parameters.buildImage }}
    inputs:
      targetType: "inline"
      script: |
        # 设置环境变量
        export CC=$CC
        export CXX=$CXX
        
        # 执行构建命令
        echo "创建构建目录..."
        mkdir -p "build/${{ parameters.architecture }}"
        
        echo "进入构建目录..."
        cd "build/${{ parameters.architecture }}"
        
        echo "配置CMake..."
        if [ "${{ parameters.architecture }}" = "arm64" ] && [ -f "../../cmake/arm64-toolchain.cmake" ]; then
          cmake ../.. -DCMAKE_BUILD_TYPE=Release -DCMAKE_TOOLCHAIN_FILE=../../cmake/arm64-toolchain.cmake
        else
          cmake ../.. -DCMAKE_BUILD_TYPE=Release
        fi
        
        echo "开始编译..."
        make -j$(nproc)
        
        # 确保构建产物在正确的目录中
        BUILD_DIR="build/${{ parameters.architecture }}"
        if [ -d "$BUILD_DIR" ]; then
          echo "构建完成，产物位于: $BUILD_DIR/"
          find "$BUILD_DIR" -type f -executable -o -name "*.so" -o -name "*.a" | head -10
        else
          echo "警告: 构建目录 $BUILD_DIR 不存在"
          echo "当前目录结构:"
          find . -name "build" -type d | head -5
        fi

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: "build/${{ parameters.architecture }}"
      artifactName: "$(artifactName)"
    displayName: "发布构建产物 (${{ parameters.architecture }})"
