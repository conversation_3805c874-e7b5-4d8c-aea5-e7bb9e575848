# 配置管理优化方案

## 问题背景

原有的 `service-config.yml` 配置文件过于复杂，包含了大量的工具配置、脚本和命令，对于管理100多个服务来说非常困难。每个服务都需要重复配置相同的工具参数和构建脚本。

## 解决方案

### 1. 配置分离

将配置分为两个层次：

- **业务配置** (`service-config.yml`) - 只包含业务决策
- **技术配置** (`templates/config/{language}-config.yml`) - 包含工具和脚本

### 2. 新的目录结构

```
├── service-config.yml              # 简化的业务配置
├── service-config-simple.yml       # 示例配置文件
├── templates/
│   ├── config/                     # 语言特定配置
│   │   ├── python-config.yml       # Python工具和脚本配置
│   │   ├── go-config.yml           # Go工具和脚本配置
│   │   └── cpp-config.yml          # C++工具和脚本配置
│   └── steps/                      # 步骤模板(已优化)
├── scripts/
│   └── config-manager.py           # 配置管理工具
└── docs/
    └── CONFIG_MANAGEMENT.md        # 本文档
```

## 配置文件对比

### 原有配置 (复杂)
```yaml
services:
  UserService:
    language: python
    staticAnalysis:
      skip: false
      tools:
        - flake8
        - pylint
      options:
        maxLineLength: 120
    build:
      architectures:
        - name: x86
          enabled: true
          image: python:3.10-slim
          commands:
            - pip install -r requirements.txt
            - python setup.py bdist_wheel
          artifactName: "python-package-x86"
    # ... 更多重复配置
```

### 新配置 (简化)
```yaml
services:
  UserService: 
    language: python
    staticAnalysis: {skip: false}
    unitTest: {skip: false, coverage: true}
    build: {architectures: [x86, arm64]}
    deploy: {ftp: {enabled: true}}
```

## 语言配置文件

### Python配置 (`templates/config/python-config.yml`)
```yaml
staticAnalysis:
  installScript: |
    pip install flake8 pylint black isort mypy
  executeScript: |
    flake8 . --count --exit-zero --max-line-length=120
    pylint src/ --exit-zero

build:
  architectures:
    x86:
      buildScript: |
        pip install wheel build
        python setup.py bdist_wheel
        mkdir -p dist/x86
        mv dist/*.whl dist/x86/
```

## 使用方式

### 1. 管理大量服务

对于100多个服务，使用紧凑语法：

```yaml
services:
  Service1: { language: python, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  Service2: { language: go, staticAnalysis: {skip: false}, unitTest: {skip: false, coverage: true}, build: {architectures: [x86, arm64]}, deploy: {ftp: {enabled: true}} }
  # ... 更多服务
```

### 2. 使用配置管理工具

```bash
# 生成模板配置
python scripts/config-manager.py generate --count 50 --output my-services.yml

# 验证配置文件
python scripts/config-manager.py validate --input service-config.yml

# 查看服务模板
python scripts/config-manager.py template --count 20
```

### 3. 批量操作

可以使用脚本批量修改配置：

```python
import yaml

# 读取配置
with open('service-config.yml', 'r') as f:
    config = yaml.safe_load(f)

# 批量启用覆盖率
for service_name, service_config in config['services'].items():
    if service_config['language'] == 'python':
        service_config['unitTest']['coverage'] = True

# 保存配置
with open('service-config.yml', 'w') as f:
    yaml.dump(config, f, default_flow_style=False)
```

## 优势

### 1. 配置简化
- 服务配置减少90%的代码量
- 只关注业务决策，不关心技术实现
- 易于批量管理和修改

### 2. 维护性提升
- 工具配置集中管理
- 升级工具版本只需修改一个文件
- 新增语言支持更容易

### 3. 一致性保证
- 所有同语言服务使用相同的工具配置
- 避免配置不一致导致的问题
- 标准化的构建和测试流程

### 4. 扩展性增强
- 新增工具只需修改语言配置文件
- 支持更复杂的脚本逻辑
- 便于添加新的构建步骤

## 迁移指南

### 1. 现有服务迁移

```bash
# 1. 备份现有配置
cp service-config.yml service-config.yml.backup

# 2. 使用工具生成新配置
python scripts/config-manager.py generate --count 100

# 3. 手动调整特殊服务的配置

# 4. 验证新配置
python scripts/config-manager.py validate --input service-config.yml
```

### 2. 步骤模板更新

步骤模板会自动从语言配置文件中读取脚本：

```yaml
# 新的步骤模板
steps:
  - task: Bash@3
    displayName: "加载Python语言配置"
    name: loadConfig
    inputs:
      script: |
        PYTHON_CONFIG=$(cat templates/config/python-config.yml)
        INSTALL_SCRIPT=$(echo "$PYTHON_CONFIG" | yq eval '.staticAnalysis.installScript' -)
        echo "##vso[task.setvariable variable=installScript;isOutput=true]$INSTALL_SCRIPT"
  
  - task: Bash@3
    displayName: "执行静态分析"
    inputs:
      script: |
        $(loadConfig.installScript)
```

## 最佳实践

1. **服务分组**: 按功能或团队对服务进行分组
2. **配置模板**: 为常见的服务类型创建配置模板
3. **版本控制**: 对配置文件进行版本控制和代码审查
4. **自动化验证**: 在CI/CD中集成配置验证
5. **文档维护**: 及时更新配置文档和示例

## 工具支持

- `scripts/config-manager.py` - 配置生成和验证工具
- `service-config-simple.yml` - 简化配置示例
- 语言配置文件 - 工具和脚本的集中管理
- 步骤模板 - 自动读取语言配置的智能模板
