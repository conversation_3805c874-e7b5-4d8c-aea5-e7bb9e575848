#!/usr/bin/env python3
"""
配置管理工具 - 用于生成和管理服务配置
支持批量生成、验证和更新服务配置
"""

import yaml
import argparse
import os
import sys
from typing import Dict, List, Any

class ConfigManager:
    def __init__(self):
        self.supported_languages = ['python', 'go', 'cpp']
        self.supported_architectures = ['x86', 'arm64']
        
    def generate_service_config(self, service_name: str, language: str, 
                              architectures: List[str] = None,
                              enable_static_analysis: bool = True,
                              enable_unit_test: bool = True,
                              enable_coverage: bool = True,
                              enable_ftp_deploy: bool = True) -> Dict[str, Any]:
        """生成单个服务的配置"""
        
        if language not in self.supported_languages:
            raise ValueError(f"不支持的语言: {language}")
        
        if architectures is None:
            architectures = ['x86', 'arm64']
        
        # 验证架构
        for arch in architectures:
            if arch not in self.supported_architectures:
                raise ValueError(f"不支持的架构: {arch}")
        
        config = {
            'language': language,
            'staticAnalysis': {
                'skip': not enable_static_analysis
            },
            'unitTest': {
                'skip': not enable_unit_test,
                'coverage': enable_coverage
            },
            'build': {
                'architectures': architectures
            },
            'deploy': {
                'ftp': {
                    'enabled': enable_ftp_deploy
                }
            }
        }
        
        return config
    
    def generate_batch_config(self, services: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量生成服务配置"""
        
        config = {
            'global': {
                'timeout': '30m',
                'retryCount': 3,
                'ftp': {
                    'server': 'ftp.example.com',
                    'port': 21,
                    'username': 'deploy_user',
                    'basePath': '/artifacts'
                }
            },
            'services': {}
        }
        
        for service in services:
            service_name = service['name']
            service_config = self.generate_service_config(
                service_name=service_name,
                language=service.get('language', 'python'),
                architectures=service.get('architectures', ['x86', 'arm64']),
                enable_static_analysis=service.get('static_analysis', True),
                enable_unit_test=service.get('unit_test', True),
                enable_coverage=service.get('coverage', True),
                enable_ftp_deploy=service.get('ftp_deploy', True)
            )
            config['services'][service_name] = service_config
        
        return config
    
    def validate_config(self, config_file: str) -> bool:
        """验证配置文件"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # 验证全局配置
            if 'global' not in config:
                print("错误: 缺少全局配置")
                return False
            
            # 验证服务配置
            if 'services' not in config:
                print("错误: 缺少服务配置")
                return False
            
            for service_name, service_config in config['services'].items():
                if not self._validate_service_config(service_name, service_config):
                    return False
            
            print(f"配置文件验证通过: {config_file}")
            return True
            
        except Exception as e:
            print(f"配置文件验证失败: {e}")
            return False
    
    def _validate_service_config(self, service_name: str, config: Dict[str, Any]) -> bool:
        """验证单个服务配置"""
        
        # 验证语言
        if 'language' not in config:
            print(f"错误: 服务 {service_name} 缺少语言配置")
            return False
        
        if config['language'] not in self.supported_languages:
            print(f"错误: 服务 {service_name} 使用了不支持的语言: {config['language']}")
            return False
        
        # 验证构建配置
        if 'build' in config and 'architectures' in config['build']:
            for arch in config['build']['architectures']:
                if arch not in self.supported_architectures:
                    print(f"错误: 服务 {service_name} 使用了不支持的架构: {arch}")
                    return False
        
        return True
    
    def generate_template_services(self, count: int = 10) -> List[Dict[str, Any]]:
        """生成模板服务列表"""
        services = []
        
        # 常见的服务类型和语言组合
        service_templates = [
            {'suffix': 'Service', 'language': 'python', 'architectures': ['x86', 'arm64']},
            {'suffix': 'API', 'language': 'go', 'architectures': ['x86', 'arm64']},
            {'suffix': 'Engine', 'language': 'cpp', 'architectures': ['x86']},
            {'suffix': 'Gateway', 'language': 'go', 'architectures': ['x86', 'arm64']},
            {'suffix': 'Worker', 'language': 'python', 'architectures': ['x86']},
        ]
        
        service_names = [
            'User', 'Order', 'Payment', 'Notification', 'Auth',
            'Config', 'Monitor', 'Log', 'Cache', 'Search',
            'File', 'Message', 'Report', 'Analytics', 'Backup'
        ]
        
        for i in range(min(count, len(service_names))):
            template = service_templates[i % len(service_templates)]
            service = {
                'name': f"{service_names[i]}{template['suffix']}",
                'language': template['language'],
                'architectures': template['architectures'],
                'static_analysis': True,
                'unit_test': True,
                'coverage': True,
                'ftp_deploy': True
            }
            services.append(service)
        
        return services

def main():
    parser = argparse.ArgumentParser(description='配置管理工具')
    parser.add_argument('command', choices=['generate', 'validate', 'template'], 
                       help='操作命令')
    parser.add_argument('--output', '-o', default='service-config.yml',
                       help='输出文件路径')
    parser.add_argument('--input', '-i', help='输入文件路径')
    parser.add_argument('--count', '-c', type=int, default=10,
                       help='生成模板服务数量')
    
    args = parser.parse_args()
    
    manager = ConfigManager()
    
    if args.command == 'generate':
        # 生成模板配置
        services = manager.generate_template_services(args.count)
        config = manager.generate_batch_config(services)
        
        with open(args.output, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        
        print(f"已生成配置文件: {args.output}")
        print(f"包含 {len(services)} 个服务")
        
    elif args.command == 'validate':
        if not args.input:
            print("错误: 验证命令需要指定输入文件 --input")
            sys.exit(1)
        
        if not os.path.exists(args.input):
            print(f"错误: 文件不存在: {args.input}")
            sys.exit(1)
        
        if manager.validate_config(args.input):
            print("配置验证通过")
        else:
            print("配置验证失败")
            sys.exit(1)
            
    elif args.command == 'template':
        # 生成服务模板列表
        services = manager.generate_template_services(args.count)
        
        print("生成的服务模板:")
        for service in services:
            print(f"  - {service['name']}: {service['language']} ({', '.join(service['architectures'])})")

if __name__ == '__main__':
    main()
