parameters:
  buildImage: ""
  artifactName: ""
  architecture: ""
  serviceName: ""
  branchName: ""
steps:
  - task: UsePythonVersion@0
    inputs:
      versionSpec: "3.10"
      architecture: ${{ parameters.architecture }}
    displayName: "安装Python (${{ parameters.architecture }})"

  - task: Bash@3
    displayName: "设置Python构建配置"
    name: loadConfig
    inputs:
      targetType: "inline"
      script: |
        ARCH='${{ parameters.architecture }}'
        BUILD_IMAGE='${{ parameters.buildImage }}'
        ARTIFACT_NAME='${{ parameters.artifactName }}'

        echo "##vso[task.setvariable variable=artifactName]$ARTIFACT_NAME"
        echo "##vso[task.setvariable variable=buildImage]$BUILD_IMAGE"

        echo "架构: $ARCH"
        echo "构建镜像: $BUILD_IMAGE"
        echo "产物名称: $ARTIFACT_NAME"

  - task: Bash@3
    displayName: "执行Python构建 (${{ parameters.architecture }})"
    container: ${{ parameters.buildImage }}
    inputs:
      targetType: "inline"
      script: |
        # 创建架构特定的输出目录
        mkdir -p "dist/${{ parameters.architecture }}"
        
        echo "安装Python依赖..."
        pip install -r requirements.txt
        
        echo "构建Python包..."
        python setup.py build
        
        echo "创建wheel包..."
        python setup.py bdist_wheel
        
        # 移动构建产物到架构特定目录
        if [ -d "dist" ]; then
          find dist -name "*.whl" -o -name "*.tar.gz" | while read file; do
            if [[ "$file" != *"/${{ parameters.architecture }}/"* ]]; then
              mv "$file" "dist/${{ parameters.architecture }}/"
            fi
          done
        fi

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: "dist/${{ parameters.architecture }}"
      artifactName: "$(artifactName)"
    displayName: "发布构建产物 (${{ parameters.architecture }})"
