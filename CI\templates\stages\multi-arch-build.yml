# 注意：静态分析和单元测试结果来自x86架构
# 构建阶段支持所有配置的架构（x86、ARM64等）
parameters:
  serviceName: ""
  branchName: ""
  language: ""
  enabledArchitectures: ""
  # 各语言和架构的镜像配置（写死）
  images:
    python:
      x86: "python:3.9-slim"
      arm64: "python:3.9-slim"
    go:
      x86: "golang:1.19-alpine"
      arm64: "golang:1.19-alpine"
    cpp:
      x86: "gcc:11-alpine"
      arm64: "gcc:11-alpine"

jobs:
  # X86架构构建
  - job: BuildX86
    displayName: "构建 X86 架构"
    pool:
      vmImage: "ubuntu-latest"
    condition: contains('${{ parameters.enabledArchitectures }}', 'x86')
    variables:
      architecture: 'x86'
    steps:
      - task: Bash@3
        displayName: "设置X86构建配置"
        name: parseConfig
        inputs:
          targetType: "inline"
          script: |
            LANGUAGE='${{ parameters.language }}'

            # 根据语言设置镜像（使用parameters中的配置）
            case "$LANGUAGE" in
              "python")
                BUILD_IMAGE="${{ parameters.images.python.x86 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-python-x86"
                ;;
              "go")
                BUILD_IMAGE="${{ parameters.images.go.x86 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-go-x86"
                ;;
              "cpp")
                BUILD_IMAGE="${{ parameters.images.cpp.x86 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-cpp-x86"
                ;;
              *)
                echo "##vso[task.logissue type=error]不支持的语言: $LANGUAGE"
                exit 1
                ;;
            esac

            echo "##vso[task.setvariable variable=buildImage]$BUILD_IMAGE"
            echo "##vso[task.setvariable variable=artifactName]$ARTIFACT_NAME"

            echo "X86 Build Image: $BUILD_IMAGE"
            echo "X86 Artifact Name: $ARTIFACT_NAME"

      - ${{ if eq(parameters.language, 'python') }}:
          - template: ../steps/build/python-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'x86'

      - ${{ if eq(parameters.language, 'go') }}:
          - template: ../steps/build/go-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'x86'

      - ${{ if eq(parameters.language, 'cpp') }}:
          - template: ../steps/build/cpp-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'x86'

  # ARM64架构构建
  - job: BuildARM64
    displayName: "构建 ARM64 架构"
    pool:
      vmImage: "ubuntu-latest"
    condition: contains('${{ parameters.enabledArchitectures }}', 'arm64')
    variables:
      architecture: 'arm64'
    steps:
      - task: Bash@3
        displayName: "设置ARM64构建配置"
        name: parseConfig
        inputs:
          targetType: "inline"
          script: |
            LANGUAGE='${{ parameters.language }}'

            # 根据语言设置镜像（使用parameters中的配置）
            case "$LANGUAGE" in
              "python")
                BUILD_IMAGE="${{ parameters.images.python.arm64 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-python-arm64"
                ;;
              "go")
                BUILD_IMAGE="${{ parameters.images.go.arm64 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-go-arm64"
                ;;
              "cpp")
                BUILD_IMAGE="${{ parameters.images.cpp.arm64 }}"
                ARTIFACT_NAME="${{ parameters.serviceName }}-cpp-arm64"
                ;;
              *)
                echo "##vso[task.logissue type=error]不支持的语言: $LANGUAGE"
                exit 1
                ;;
            esac

            echo "##vso[task.setvariable variable=buildImage]$BUILD_IMAGE"
            echo "##vso[task.setvariable variable=artifactName]$ARTIFACT_NAME"

            echo "ARM64 Build Image: $BUILD_IMAGE"
            echo "ARM64 Artifact Name: $ARTIFACT_NAME"

      - ${{ if eq(parameters.language, 'python') }}:
          - template: ../steps/build/python-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'arm64'

      - ${{ if eq(parameters.language, 'go') }}:
          - template: ../steps/build/go-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'arm64'

      - ${{ if eq(parameters.language, 'cpp') }}:
          - template: ../steps/build/cpp-build-arch.yml
            parameters: 
              branchName: ${{ parameters.branchName }}
              serviceName: ${{ parameters.serviceName }}
              buildImage: $(buildImage)
              artifactName: $(artifactName)
              architecture: 'arm64'
